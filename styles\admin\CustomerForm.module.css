.customerForm {
  width: 100%;
}

.error {
  background-color: rgba(255, 0, 0, 0.1);
  color: #d32f2f;
  padding: 12px;
  border-radius: 4px;
  margin-bottom: 16px;
  border-left: 4px solid #d32f2f;
}

.success {
  background-color: rgba(76, 175, 80, 0.1);
  color: #4caf50;
  padding: 12px;
  border-radius: 4px;
  margin-bottom: 16px;
  border-left: 4px solid #4caf50;
}

.formGrid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
  margin-bottom: 24px;
}

.formGroup {
  margin-bottom: 16px;
}

.formGroup label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #555;
}

.formRow {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
  margin-bottom: 16px;
}

.fieldHelp {
  display: block;
  margin-top: 4px;
  font-size: 0.85rem;
  color: #666;
  font-style: italic;
}

.input, .select, .textarea {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
  transition: border-color 0.2s ease;
}

.input:focus, .select:focus, .textarea:focus {
  border-color: #6e8efb;
  outline: none;
}

.textarea {
  resize: vertical;
  min-height: 100px;
}

.checkboxGroup {
  display: flex;
  align-items: center;
  margin-bottom: 24px;
}

.checkbox {
  margin-right: 8px;
}

.managementSection, .marketingSection, .preferencesSection {
  margin-bottom: 30px;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 24px;
  background: #fafbfc;
}

.managementSection h3, .marketingSection h3, .preferencesSection h3 {
  margin-top: 0;
  margin-bottom: 20px;
  font-size: 1.3rem;
  color: #333;
  font-weight: 600;
  border-bottom: 2px solid #6e8efb;
  padding-bottom: 8px;
}

.preferencesList {
  margin-bottom: 16px;
}

.preferenceItem {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background-color: #f9f9f9;
  border-radius: 4px;
  margin-bottom: 8px;
}

.preferenceContent {
  flex: 1;
}

.removeButton {
  background: none;
  border: none;
  color: #d32f2f;
  font-size: 1.2rem;
  cursor: pointer;
  padding: 0 8px;
}

.addPreference {
  display: flex;
  gap: 12px;
}

.preferenceInputs {
  display: flex;
  gap: 12px;
  flex: 1;
}

.addButton {
  background-color: #6e8efb;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 0 16px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.addButton:hover:not(:disabled) {
  background-color: #5a7df9;
}

.addButton:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.formActions {
  display: flex;
  justify-content: flex-end;
  gap: 16px;
  margin-top: 24px;
  padding-top: 24px;
  border-top: 1px solid #eaeaea;
}

.cancelButton {
  padding: 10px 20px;
  background-color: #f5f5f5;
  border: 1px solid #ddd;
  border-radius: 4px;
  color: #555;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.cancelButton:hover:not(:disabled) {
  background-color: #e5e5e5;
}

.saveButton {
  padding: 10px 20px;
  background: linear-gradient(135deg, #6e8efb, #a777e3);
  border: none;
  border-radius: 4px;
  color: white;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.saveButton:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.saveButton:disabled, .cancelButton:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

@media (max-width: 768px) {
  .formGrid {
    grid-template-columns: 1fr;
  }

  .formRow {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .managementSection, .marketingSection, .preferencesSection {
    padding: 20px;
  }

  .preferenceInputs {
    flex-direction: column;
  }

  .addPreference {
    flex-direction: column;
  }

  .formActions {
    flex-direction: column-reverse;
  }

  .saveButton, .cancelButton {
    width: 100%;
  }
}
